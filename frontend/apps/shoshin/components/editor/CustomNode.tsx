"use client"

import { <PERSON><PERSON>, Position, type NodeProps } from "@xyflow/react"
import { Play } from "lucide-react"
import { getAllBlocks, getAllCoreBlocks } from "../../blocks"
import { useEditorStore } from "../../stores/editorStore"
import { useNodeStateStore } from "../../stores/nodeStateStore"
import { NodeActionBar } from "./NodeActionBar"

// Create dynamic icon map from core blocks and tool blocks
const createIconMap = () => {
  const coreBlocks = getAllCoreBlocks()
  const toolBlocks = getAllBlocks()
  const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {}

  // Add core block icons
  coreBlocks.forEach(block => {
    iconMap[block.id] = block.icon
  })

  // Add tool block icons
  toolBlocks.forEach(block => {
    iconMap[block.type] = block.icon
  })

  return iconMap
}

const iconMap = createIconMap()

// Helper function to convert hex color to Tailwind class
const hexToTailwindClass = (hexColor: string): string => {
  // Map common hex colors to Tailwind classes
  const colorMap: Record<string, string> = {
    '#802FFF': 'bg-purple-600',
    '#2F55FF': 'bg-blue-600',
    '#FF752F': 'bg-orange-500',
    '#FF402F': 'bg-red-500',
    '#28C43F': 'bg-green-500',
    '#F64F9E': 'bg-pink-500',
    '#1F40ED': 'bg-blue-700',
    '#FFC83C': 'bg-yellow-500',
    '#4D5FFF': 'bg-indigo-500',
    '#2FB3FF': 'bg-cyan-500',
    '#6B7280': 'bg-gray-500',
    '#4D5FFF': 'bg-indigo-500',
    '#FF4B4B': 'bg-red-500',
    '#0066FF': 'bg-blue-600',
    '#E0E0E0': 'bg-gray-400',
    '#FF5700': 'bg-orange-600',
    '#40916C': 'bg-green-600',
  }

  return colorMap[hexColor] || 'bg-gray-500'
}

// Create dynamic color map from core blocks and tool blocks
const createColorMap = () => {
  const coreBlocks = getAllCoreBlocks()
  const toolBlocks = getAllBlocks()
  const colorMap: Record<string, string> = {}

  // Add core block colors
  coreBlocks.forEach(block => {
    colorMap[block.id] = hexToTailwindClass(block.color)
  })

  // Add tool block colors
  toolBlocks.forEach(block => {
    colorMap[block.type] = hexToTailwindClass(block.bgColor)
  })

  return colorMap
}

const colorMap = createColorMap()

interface CustomNodeData {
  label: string
  type: string
  description?: string
}

export function CustomNode({ data, selected, id }: NodeProps) {
  const highlightedNodes = useEditorStore((state) => state.highlightedNodes)
  const isHighlighted = highlightedNodes.includes(id)

  // Get node action functions from the store
  const {
    toggleNodeEnabled,
    duplicateNode,
    toggleNodePorts,
    deleteNode,
    openNodeSettings,
  } = useEditorStore()

  // Get node state from nodeStateStore for user-set values
  const { getNodeState } = useNodeStateStore()
  const nodeState = getNodeState(id)

  const nodeData = data as CustomNodeData
  const Icon = iconMap[nodeData.type as keyof typeof iconMap] || Play
  const bgColor = colorMap[nodeData.type as keyof typeof colorMap] || "bg-neutral-600"

  // Get node state - prioritize nodeStateStore values over node data
  const isEnabled = nodeState?.settings.enabled ?? nodeData.enabled ?? true
  const hasVerticalPorts = nodeState?.settings.hasVerticalPorts ?? nodeData.hasVerticalPorts ?? false

  // Get the label from nodeStateStore if available, otherwise fall back to node data
  const displayLabel = nodeState?.settings.label || nodeData.label

  // Determine the border and shadow styles based on selection and highlighting
  const getBorderStyles = () => {
    if (selected) {
      return "border-primary-500 shadow-lg shadow-primary-500/20"
    }
    if (isHighlighted) {
      return "border-teal-400 shadow-lg shadow-teal-400/25 ring-2 ring-teal-400/15"
    }
    return "border-border hover:border-primary-500/50 hover:shadow-md"
  }

  return (
    <div className={`group relative px-4 py-3 rounded-md bg-background border-2 min-w-[240px] transition-all duration-300 ${getBorderStyles()} ${!isEnabled ? 'opacity-60' : ''}`}>
      {/* Node Action Bar */}
      <NodeActionBar
        nodeId={id}
        nodeType={nodeData.type}
        isEnabled={isEnabled}
        hasVerticalPorts={hasVerticalPorts}
        onToggleEnabled={toggleNodeEnabled}
        onDuplicate={duplicateNode}
        onTogglePorts={toggleNodePorts}
        onDelete={deleteNode}
        onSettings={openNodeSettings}
      />

      <Handle
        type="target"
        position={hasVerticalPorts ? Position.Top : Position.Left}
        className={`${hasVerticalPorts ? '!w-5 !h-[7px] !top-[-7px]' : '!w-[7px] !h-5 !left-[-7px]'} !bg-blue-400 dark:!bg-blue-500 !rounded-[2px] !border-none !z-[30] group-hover:!shadow-[0_0_0_3px_rgba(59,130,246,0.15)] ${hasVerticalPorts ? 'hover:!h-[10px] hover:!top-[-10px] hover:!rounded-t-full hover:!rounded-b-none' : 'hover:!w-[10px] hover:!left-[-10px] hover:!rounded-l-full hover:!rounded-r-none'} hover:!bg-blue-500 dark:hover:!bg-blue-400 !cursor-crosshair transition-[colors] duration-150`}
        style={{
          ...(hasVerticalPorts
            ? { left: '50%', transform: 'translateX(-50%)' }
            : { top: '50%', transform: 'translateY(-50%)' }),
        }}
      />

      <div className="flex items-center space-x-3">
        <div className={`w-10 h-10 ${bgColor} rounded-md flex items-center justify-center flex-shrink-0 shadow-sm ${!isEnabled ? 'grayscale' : ''}`}>
          <Icon className="w-5 h-5 text-white" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="text-sm font-semibold text-foreground truncate">
            {displayLabel}
          </div>
          {nodeData.description && (
            <div className="text-xs text-muted-foreground truncate mt-0.5">
              {nodeData.description}
            </div>
          )}
        </div>
      </div>

      <Handle
        type="source"
        position={hasVerticalPorts ? Position.Bottom : Position.Right}
        className={`${hasVerticalPorts ? '!w-5 !h-[7px] !bottom-[-7px]' : '!w-[7px] !h-5 !right-[-7px]'} !bg-green-400 dark:!bg-green-500 !rounded-[2px] !border-none !z-[30] group-hover:!shadow-[0_0_0_3px_rgba(34,197,94,0.15)] ${hasVerticalPorts ? 'hover:!h-[10px] hover:!bottom-[-10px] hover:!rounded-b-full hover:!rounded-t-none' : 'hover:!w-[10px] hover:!right-[-10px] hover:!rounded-r-full hover:!rounded-l-none'} hover:!bg-green-500 dark:hover:!bg-green-400 !cursor-crosshair transition-[colors] duration-150`}
        style={{
          ...(hasVerticalPorts
            ? { left: '50%', transform: 'translateX(-50%)' }
            : { top: '50%', transform: 'translateY(-50%)' }),
        }}
      />
    </div>
  )
}
